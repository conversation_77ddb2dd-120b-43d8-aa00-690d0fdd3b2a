<template>
  <div class="item-detail-modal-wrapper">
    <!-- SVG图标定义 -->
    <svg style="display: none;">
      <defs>
        <symbol id="heart-filled" viewBox="0 0 24 24">
          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="currentColor"/>
        </symbol>
        <symbol id="heart-outline" viewBox="0 0 24 24">
          <path d="M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3 4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5 22 5.42 19.58 3 16.5 3zm-4.4 15.55l-.1.1-.1-.1C7.14 14.24 4 11.39 4 8.5 4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5 0 2.89-3.14 5.74-7.9 10.05z" fill="currentColor"/>
        </symbol>
        <symbol id="share" viewBox="0 0 24 24">
          <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z" fill="currentColor"/>
        </symbol>
        <symbol id="arrow-up" viewBox="0 0 24 24">
          <path d="M7 14l5-5 5 5z" fill="currentColor"/>
        </symbol>
        <symbol id="arrow-down" viewBox="0 0 24 24">
          <path d="M7 10l5 5 5-5z" fill="currentColor"/>
        </symbol>
        <symbol id="minus" viewBox="0 0 24 24">
          <path d="M19 13H5v-2h14v2z" fill="currentColor"/>
        </symbol>
        <symbol id="file-text" viewBox="0 0 24 24">
          <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z" fill="currentColor"/>
        </symbol>
      </defs>
    </svg>

    <n-modal
    v-model:show="visible"
    preset="card"
    class="item-detail-modal"
    :style="{ width: '92%', maxWidth: '1600PX', marginTop: '3vh' }"
    :title="item?.object_name || '物品详情'"
    :bordered="true"
    size="huge"
    :closable="true"
    :mask-closable="true"
    :auto-focus="false"
    transform-origin="center"
    content-style="padding: 0; height: 85vh; overflow: hidden;"
  >
    <template #header>
      <div class="modal-header" >
        <div class="header-title-section">
          <h2 class="header-title">{{ item?.object_name || '物品详情' }}</h2>
          <n-tag
            v-if="item"
            class="header-grade-tag"
            :type="getGradeTagType(item.grade)"
            size="small"
            :bordered="true"
          >
            {{ formatGrade(item.grade) }}
          </n-tag>
        </div>
      </div>
    </template>

    <template #header-extra>
      <n-space size="small">
        <n-tooltip trigger="hover" placement="bottom">
          <template #trigger>
            <n-button
              secondary
              size="small"
              :loading="favoriteLoading"
              @click="toggleFavorite"
              class="action-button"
            >
              <template #icon>
                <n-icon size="18">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <use :href="`#${isFavorite ? 'heart-filled' : 'heart-outline'}`" />
                  </svg>
                </n-icon>
              </template>
              {{ isFavorite ? '已收藏' : '收藏' }}
            </n-button>
          </template>
          {{ isFavorite ? '取消收藏' : '收藏物品' }}
        </n-tooltip>
        
        <n-tooltip trigger="hover" placement="bottom">
          <template #trigger>
            <n-button
              secondary
              size="small"
              @click="shareItem"
              class="action-button"
            >
              <template #icon>
                <n-icon size="18">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <use href="#share" />
                  </svg>
                </n-icon>
              </template>
              分享
            </n-button>
          </template>
          分享物品
        </n-tooltip>
      </n-space>
    </template>

    <div v-if="loading" class="item-detail-loading">
      <loading-skeleton type="default" />
    </div>

    <div v-else-if="item" class="item-detail-scroll-container" style="margin-bottom: 20px;">
      <div class="item-detail-layout">
        <!-- 左侧列：物品完整信息卡片 (35%) -->
        <div class="item-detail-left-column">
          <!-- 物品完整信息卡片 -->
          <n-card class="item-overview-card" size="large">
            <div class="item-image-section">
              <div
                class="item-image-border"
                :class="`grade-border-${item.grade}`"
              >
                <img
                  :src="item.pic"
                  :alt="item.object_name"
                  class="item-image"
                  @load="handleImageLoad"
                />
                <div class="grade-badge" :class="`grade-badge-${item.grade}`">
                  等级{{ item.grade }}
                </div>
              </div>
            </div>

            <!-- 物品标题 -->
            <div class="item-header">
              <div class="item-info-block">
                <div class="item-title-row">
                  <span class="item-title">{{ item.object_name }}</span>
                </div>
                <div class="item-price-row">
                  <span class="item-price">{{ item.price_change.price.toFixed(2) }} 哈夫币</span>
                </div>
                <div class="item-change-row">
                  <span
                    class="item-change"
                    :class="{
                      'item-change-up': item.price_24h_ago > 0,
                      'item-change-down': item.price_24h_ago < 0
                    }"
                  >
                    <n-icon size="16">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <use :href="`#${getPriceChangeIcon(item.price_24h_ago)}`" />
                      </svg>
                    </n-icon>
                    24小时对比： {{ item.price_24h_ago > 0 ? '+' : '-' }}{{ formatPriceChange(item.price_24h_ago) }} 哈夫币
                    <span class="item-change-percent">
                      {{ formatPriceChangePercentage(item.price_change.price, item.price_24h_ago) }}
                    </span>
                  </span>
                </div>
              </div>
            </div>

            <!-- 属性列表 -->
            <div class="attr-list">
              <!-- 物理属性 -->
              <div class="attr-table-group" v-if="item.length && item.width">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">物理属性</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row">
                    <span class="attr-table-label">尺寸：</span>
                    <span class="attr-table-value">
                      {{ item.length }} × {{ item.width }}
                      <span
                        class="size-grid"
                        :style="{
                          gridTemplateColumns: `repeat(${item.length}, 14px)`,
                          gridTemplateRows: `repeat(${item.width}, 14px)`
                        }"
                      >
                        <span
                          v-for="n in item.length * item.width"
                          :key="n"
                          class="size-grid-cell"
                          :class="`grade-bg-${item.grade}`"
                        ></span>
                      </span>
                    </span>
                  </div>
                  <div class="attr-table-row" v-if="item.weight">
                    <span class="attr-table-label">重量：</span>
                    <span class="attr-table-value">{{ item.weight }}kg</span>
                  </div>
                </div>
              </div>

              <!-- 武器参数 -->
              <div class="attr-table-group" v-if="item.extraDetails">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">武器参数</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row" v-if="item.extraDetails.caliber">
                    <span class="attr-table-label">口径：</span>
                    <span class="attr-table-value">{{ item.extraDetails.caliber }}</span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.fire_mode">
                    <span class="attr-table-label">射击模式：</span>
                    <span class="attr-table-value">{{ item.extraDetails.fire_mode }}</span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.capacity">
                    <span class="attr-table-label">弹匣容量：</span>
                    <span class="attr-table-value">{{ item.extraDetails.capacity }}</span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.muzzle_velocity">
                    <span class="attr-table-label">射速：</span>
                    <span class="attr-table-value">{{ item.extraDetails.muzzle_velocity }}</span>
                  </div>
                </div>
              </div>

              <!-- 伤害与性能（带进度条） -->
              <div class="attr-table-group" v-if="item.extraDetails">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">伤害与性能</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row" v-if="item.extraDetails.meat_harm">
                    <span class="attr-table-label">肉伤害：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.meat_harm }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.meat_harm, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#f44336"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.armor_harm">
                    <span class="attr-table-label">护甲伤害：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.armor_harm }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.armor_harm, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#2196f3"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.shoot_distance">
                    <span class="attr-table-label">射程：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.shoot_distance }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.shoot_distance, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#4caf50"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.recoil">
                    <span class="attr-table-label">后座力：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.recoil }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.recoil, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#ff9800"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                </div>
              </div>

              <!-- 控制性能（带进度条） -->
              <div class="attr-table-group" v-if="item.extraDetails">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">控制性能</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row" v-if="item.extraDetails.control">
                    <span class="attr-table-label">控制性：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.control }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.control, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#00bcd4"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.stable">
                    <span class="attr-table-label">稳定性：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.stable }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.stable, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#607d8b"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.blind_shot">
                    <span class="attr-table-label">盲射性能：</span>
                    <span class="attr-table-value-bar">
                      <span class="attr-table-value">{{ item.extraDetails.blind_shot }}</span>
                      <span class="attr-table-bar">
                        <n-progress
                          type="line"
                          :percentage="getPercent(item.extraDetails.blind_shot, 100)"
                          :show-indicator="false"
                          height="8"
                          color="#8bc34a"
                          style="width: 120px; margin-left: 10px;"
                        />
                      </span>
                    </span>
                  </div>
                </div>
              </div>

              <!-- 其他参数 -->
              <div class="attr-table-group" v-if="item.extraDetails">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">其他参数</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row" v-if="item.extraDetails.muzzle_velocity">
                    <span class="attr-table-label">初速：</span>
                    <span class="attr-table-value">{{ item.extraDetails.muzzle_velocity }}<span v-if="item.extraDetails.muzzle_velocity"> 米/秒</span></span>
                  </div>
                  <div class="attr-table-row" v-if="item.extraDetails.sound_distance">
                    <span class="attr-table-label">声音距离：</span>
                    <span class="attr-table-value">{{ item.extraDetails.sound_distance }}<span v-if="item.extraDetails.sound_distance"> 米</span></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 子弹属性（表格风格） -->
            <div class="attr-table-group" v-if="item.ammoDetails">
              <div class="attr-table-title">
                <span class="attr-table-title-line"></span>
                <span class="attr-table-title-text">子弹属性</span>
                <span class="attr-table-title-line"></span>
              </div>
              <div class="attr-table-list">
                <div class="attr-table-row" v-if="item.ammoDetails.penetration_level">
                  <span class="attr-table-label">穿透等级：</span>
                  <span class="attr-table-value">{{ item.ammoDetails.penetration_level }}</span>
                </div>
                <div class="attr-table-row" v-if="item.ammoDetails.harm_ratio">
                  <span class="attr-table-label">伤害比例：</span>
                  <span class="attr-table-value">{{ item.ammoDetails.harm_ratio }}%</span>
                </div>
                <div class="attr-table-row" v-if="item.ammoDetails.armor_harm_level">
                  <span class="attr-table-label">破甲等级：</span>
                  <span class="attr-table-value">{{ item.ammoDetails.armor_harm_level }}</span>
                </div>
              </div>
            </div>

            <!-- 配件属性（表格风格） -->
            <div class="attr-table-group" v-if="item.accessoryDetails">
              <div class="attr-table-title">
                <span class="attr-table-title-line"></span>
                <span class="attr-table-title-text">配件属性</span>
                <span class="attr-table-title-line"></span>
              </div>
              <div class="attr-table-list">
                <div class="attr-table-row" v-if="item.accessoryDetails.recoil !== undefined && item.accessoryDetails.recoil !== null">
                  <span class="attr-table-label">后坐力控制：</span>
                  <span class="attr-table-value">{{ item.accessoryDetails.recoil > 0 ? '+' : '' }}{{ item.accessoryDetails.recoil }}</span>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.control_speed !== undefined && item.accessoryDetails.control_speed !== null">
                  <span class="attr-table-label">操控速度：</span>
                  <span class="attr-table-value">{{ item.accessoryDetails.control_speed > 0 ? '+' : '' }}{{ item.accessoryDetails.control_speed }}</span>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.quick_separate !== undefined && item.accessoryDetails.quick_separate !== null">
                  <span class="attr-table-label">快速拆卸：</span>
                  <span class="attr-table-value">{{ item.accessoryDetails.quick_separate ? '是' : '否' }}</span>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.hip_shot !== undefined && item.accessoryDetails.hip_shot !== null">
                  <span class="attr-table-label">腰际射击：</span>
                  <span class="attr-table-value">{{ item.accessoryDetails.hip_shot > 0 ? '+' : '' }}{{ item.accessoryDetails.hip_shot }}</span>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.control_stable !== undefined && item.accessoryDetails.control_stable !== null">
                  <span class="attr-table-label">控制稳定性：</span>
                  <span class="attr-table-value">{{ item.accessoryDetails.control_stable > 0 ? '+' : '' }}{{ item.accessoryDetails.control_stable }}</span>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.bomb_capacity !== undefined && item.accessoryDetails.bomb_capacity !== null">
                  <span class="attr-table-label">弹药容量：</span>
                  <span class="attr-table-value">{{ item.accessoryDetails.bomb_capacity > 0 ? '+' : '' }}{{ item.accessoryDetails.bomb_capacity }}</span>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.effect_list">
                  <span class="attr-table-label">效果列表：</span>
                  <div class="attr-table-value">
                    <div v-for="(effect, index) in parseEffectList(item.accessoryDetails.effect_list)" :key="index" class="effect-item">
                      <span class="effect-value">{{ effect.value }}</span>
                      <span v-if="effect.batteryValue" class="effect-battery" :class="effect.batteryColor">
                        {{ effect.batteryValue > 0 ? '+' : '' }}{{ effect.batteryValue }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="attr-table-row" v-if="item.accessoryDetails.advantage_effect_list">
                  <span class="attr-table-label">劣势效果：</span>
                  <div class="attr-table-value">
                    <div v-for="(effect, index) in parseEffectList(item.accessoryDetails.advantage_effect_list)" :key="index" class="effect-item">
                      <span class="effect-value">{{ effect.value }}</span>
                      <span v-if="effect.batteryValue" class="effect-battery" :class="effect.batteryColor">
                        {{ effect.batteryValue > 0 ? '+' : '' }}{{ effect.batteryValue }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 特殊属性（表格风格，和物理属性一致） -->
            <div class="attr-table-group" v-if="item.propDetails">
              <div class="attr-table-title">
                <span class="attr-table-title-line"></span>
                <span class="attr-table-title-text">特殊属性</span>
                <span class="attr-table-title-line"></span>
              </div>
              <div class="attr-table-list">
                <div class="attr-table-row" v-if="item.propDetails.type">
                  <span class="attr-table-label">类型：</span>
                  <span class="attr-table-value">{{ item.propDetails.type }}</span>
                </div>
                <div class="attr-table-row" v-if="item.propDetails.props_source">
                  <span class="attr-table-label">来源：</span>
                  <span class="attr-table-value">{{ item.propDetails.props_source }}</span>
                </div>
                <div class="attr-table-row" v-if="item.propDetails.effect">
                  <span class="attr-table-label">效果：</span>
                  <span class="attr-table-value">{{ item.propDetails.effect }}</span>
                </div>
                <div class="attr-table-row" v-if="item.propDetails.durability">
                  <span class="attr-table-label">耐久度：</span>
                  <span class="attr-table-value">{{ item.propDetails.durability }}</span>
                </div>
                <div class="attr-table-row" v-if="item.propDetails.stack_limit">
                  <span class="attr-table-label">堆叠限制：</span>
                  <span class="attr-table-value">{{ item.propDetails.stack_limit }}</span>
                </div>
              </div>
            </div>

            <!-- 物品属性区域（表格风格，分组） -->
            <div v-if="item.protectDetails || (item.ammoDetailsList && Object.keys(item.ammoDetailsList).length > 0) || (item.accessorySlots && item.accessorySlots.length > 0)" class="attr-table-group">
              <!-- 护甲属性分组 -->
              <div v-if="item.protectDetails" class="attr-table-group">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">护甲属性</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row"><span class="attr-table-label">防护等级：</span><span class="attr-table-value">{{ item.protectDetails.protect_level }}</span></div>
                  <div class="attr-table-row"><span class="attr-table-label">耐久度：</span><span class="attr-table-value">{{ item.protectDetails.durability }}</span></div>
                  <div class="attr-table-row"><span class="attr-table-label">防护区域：</span><span class="attr-table-value">{{ item.protectDetails.protect_area }}</span></div>
                  <div class="attr-table-row"><span class="attr-table-label">耐久损失：</span><span class="attr-table-value">{{ item.protectDetails.durable_loss }}</span></div>
                </div>
              </div>
              <!-- 护甲效果分组 -->
              <div v-if="item.protectDetails" class="attr-table-group">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">护甲效果</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div class="attr-table-row"><span class="attr-table-label">移动速度：</span><span class="attr-table-value">{{ item.protectDetails.move_speed_percent }}% <span v-if="item.protectDetails.move_speed_battery"></span></span></div>
                  <div class="attr-table-row"><span class="attr-table-label">瞄准速度：</span><span class="attr-table-value">{{ item.protectDetails.aim_speed_percent }}% <span v-if="item.protectDetails.aim_speed_battery"></span></span></div>
                  <div class="attr-table-row"><span class="attr-table-label">声音效果：</span><span class="attr-table-value">{{ item.protectDetails.sound_effect_percent }}% <span v-if="item.protectDetails.sound_effect_battery"></span></span></div>
                  <div class="attr-table-row"><span class="attr-table-label">面部防护：</span><span class="attr-table-value">{{ item.protectDetails.face_mask_value }}</span></div>
                  <div class="attr-table-row"><span class="attr-table-label">其他效果：</span><span class="attr-table-value">{{ item.protectDetails.repair_efficiency }}</span></div>
                </div>
              </div>
              <!-- 弹药分组 -->
              <div v-if="item.ammoDetailsList && Object.keys(item.ammoDetailsList).length > 0" class="attr-table-group">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">可用弹药</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div v-for="(ammo, ammoId) in item.ammoDetailsList" :key="ammoId" class="attr-table-row">
                    <span class="attr-table-label">{{ ammo.object_name }}：</span>
                    <span class="attr-table-value">
                      穿甲等级: {{ ammo.penetration_level || '-' }}，护甲伤害: {{ ammo.armor_harm_level || '-' }}，当前售价: <span v-if="ammo.price !== undefined">{{ ammo.price > 0 ? ammo.price.toFixed(2) : '暂无数据' }}</span><span v-else>-</span>
                    </span>
                  </div>
                </div>
              </div>
              <!-- 配件槽分组 -->
              <div v-if="item.accessorySlots && item.accessorySlots.length > 0" class="attr-table-group">
                <div class="attr-table-title">
                  <span class="attr-table-title-line"></span>
                  <span class="attr-table-title-text">配件槽位</span>
                  <span class="attr-table-title-line"></span>
                </div>
                <div class="attr-table-list">
                  <div v-for="slot in item.accessorySlots" :key="slot.id" class="attr-table-row">
                    <span class="attr-table-label">{{ slot.slot_name }}：</span>
                    <span class="attr-table-value">类型 {{ slot.slot_type }}，可安装</span>
                  </div>
                </div>
              </div>
            </div>
          </n-card>
        </div>

        <!-- 右侧列：价格统计和详细属性 (65%) -->
        <div class="item-detail-right-column">
          <!-- 价格历史组件放最上面 -->
          <ItemPriceHistoryECharts
            :object-id="item?.object_id"
            :current-price="item?.price_change?.price"
            :price24h-ago="item?.price_24h_ago"
            :grade="item?.grade"
            :loading="loading"
            v-if="item"
            @chart-click="handleEnlargeChart"
          />

          <!-- 物品描述区域 -->
          <n-card class="content-section" embedded>
            <template #header>
              <n-space align="center">
                <n-icon size="18">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <use href="#file-text" />
                  </svg>
                </n-icon>
                <n-text strong>物品描述</n-text>
              </n-space>
            </template>

            <div class="description-content">
              <div v-if="item.desc" class="description-text">
                <n-text>{{ item.desc }}</n-text>
              </div>
              <div v-else class="no-description">
                <n-empty description="暂无描述信息" size="small" />
              </div>
            </div>
          </n-card>
        </div>
      </div>
    </div>

    <div v-else class="item-detail-error">
      <n-result 
        status="404" 
        title="物品未找到" 
        description="抱歉，未能找到该物品的详细信息"
      >
        <template #footer>
          <n-button secondary @click="visible = false">返回</n-button>
        </template>
      </n-result>
    </div>

    <!-- 放大图表弹窗 -->
    <n-modal
      v-model:show="showChartModal"
      :style="{  maxWidth: '95vw', height:'90vh'}"
      :mask-closable="true"
      :closable="true"
      preset="card"
      :title="'价格历史'"
      class="enlarge-chart-modal"
    >
      <ItemPriceHistoryECharts
        :object-id="item?.object_id"
        :current-price="item?.price_change?.price"
        :price24h-ago="item?.price_24h_ago"
        :grade="item?.grade"
        :loading="loading"
        v-if="item"
        :enlarge="true"
      />
    </n-modal>
  </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { getItemDetail } from '@/api/items'

import ItemPriceHistoryECharts from './ItemPriceHistoryECharts.vue'
import LoadingSkeleton from '@/components/common/LoadingSkeleton.vue'

interface Props {
  objectId?: number | null
  show?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  objectId: null,
  show: false
})

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
}>()

const message = useMessage()

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const item = ref<any>(null)
const loading = ref(false)
const favoriteLoading = ref(false)
const isFavorite = ref(false)
const showChartModal = ref(false)









// 放大图表
const handleEnlargeChart = () => {
  showChartModal.value = true
}

// 获取物品详情
const fetchItemDetail = async () => {
  if (!props.objectId) {
    console.warn('ItemDetailModal: objectId is null or undefined')
    return
  }

  console.log('ItemDetailModal: 开始获取物品详情, objectId:', props.objectId)
  loading.value = true

  try {
    const response = await getItemDetail(props.objectId)
    console.log('ItemDetailModal: API响应:', response)

    if (response.code === 1) {
      item.value = response.data
      console.log('ItemDetailModal: 物品详情获取成功:', response.data)
    } else {
      console.error('ItemDetailModal: API返回错误:', response.msg || response.message)
      message.error(`获取物品详情失败: ${response.msg || response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('ItemDetailModal: 获取物品详情异常:', error)
    message.error(`获取物品详情异常: ${error instanceof Error ? error.message : '网络错误'}`)
  } finally {
    loading.value = false
  }
}

// 监听objectId变化
watch(() => props.objectId, (newId, oldId) => {
  console.log('ItemDetailModal: objectId变化:', { oldId, newId, show: props.show })
  if (newId && props.show) {
    fetchItemDetail()
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(() => props.show, (show, oldShow) => {
  console.log('ItemDetailModal: show状态变化:', { oldShow, show, objectId: props.objectId })
  if (show && props.objectId) {
    fetchItemDetail()
  }
})

// 切换收藏状态
const toggleFavorite = async () => {
  favoriteLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    isFavorite.value = !isFavorite.value
    message.success(isFavorite.value ? '已添加到收藏' : '已取消收藏')
  } catch (error) {
    message.error('操作失败')
  } finally {
    favoriteLoading.value = false
  }
}

// 分享物品
const shareItem = () => {
  if (navigator.share) {
    navigator.share({
      title: item.value?.object_name,
      text: `查看物品详情：${item.value?.object_name}`,
      url: window.location.href
    })
  } else {
    navigator.clipboard.writeText(window.location.href)
    message.success('链接已复制到剪贴板')
  }
}


// 处理图片加载完成
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 图片加载完成后的处理逻辑

}

// 修改价格变化图标函数
const getPriceChangeIcon = (price24hAgo: number) => {
  if (!item.value) return 'minus'
  const currentPrice = item.value.price_change.price
  const change = currentPrice - price24hAgo
  if (change > 0) return 'arrow-up'
  if (change < 0) return 'arrow-down'
  return 'minus'
}

// 修改价格变化格式化函数
const formatPriceChange = (change: number) => {
  if (!change) return '0.00'
  return `${Math.abs(change).toFixed(2)}`
}

// 修改价格变化百分比格式化函数
const formatPriceChangePercentage = (currentPrice: number, change: number) => {
  if (!change || !currentPrice) return '0.00%'
  const percentage = (change / (currentPrice - change)) * 100
  const sign = percentage > 0 ? '+' : '-'
  return `${sign}${Math.abs(percentage).toFixed(2)}%`
}

// 格式化等级
const formatGrade = (grade: number) => {
  return `等级${grade}`
}

// 获取等级标签类型
const getGradeTagType = (grade: number) => {
  const typeMap: Record<number, string> = {
    1: 'default',
    2: 'success',
    3: 'info',
    4: 'warning',
    5: 'warning',
    6: 'error'
  }
  return typeMap[grade] || 'default'
}

// 获取穿透等级标签类型
const getPenetrationTagType = (level: number | string) => {
  const numLevel = typeof level === 'string' ? parseInt(level) : level
  if (isNaN(numLevel)) return 'default'
  if (numLevel <= 2) return 'default'
  if (numLevel <= 3) return 'success'
  if (numLevel <= 4) return 'info'
  if (numLevel <= 5) return 'warning'
  return 'error'
}

// 获取护甲伤害标签类型
const getDamageTagType = (damage: string | number) => {
  if (!damage) return 'default'
  const damageStr = String(damage).toLowerCase()
  if (damageStr === '低' || damageStr === 'low') return 'success'
  if (damageStr === '中' || damageStr === 'medium') return 'warning'
  if (damageStr === '高' || damageStr === 'high') return 'error'
  return 'default'
}

// 解析效果列表
const parseEffectList = (effectList: any) => {
  if (!effectList) return []

  try {
    let parsed
    if (typeof effectList === 'string') {
      parsed = JSON.parse(effectList)
    } else {
      parsed = effectList
    }

    // 如果是对象，转换为数组
    if (typeof parsed === 'object' && !Array.isArray(parsed)) {
      return Object.values(parsed)
    }

    // 如果已经是数组，直接返回
    if (Array.isArray(parsed)) {
      return parsed
    }

    return []
  } catch (error) {
    console.error('解析效果列表失败:', error)
    return []
  }
}

// 计算进度条百分比
function getPercent(val: number | string, max: number) {
  const num = Number(val)
  if (isNaN(num)) return 0
  return Math.max(0, Math.min(100, (num / max) * 100))
}
</script>

<style scoped>
/* 包装器样式 - 确保不影响现有布局 */
.item-detail-modal-wrapper {
  /* 透明包装器，不影响布局 */
}

/* 优化布局高度和滚动 */
.item-detail-layout {
  display: flex;
  width: 100%;
  gap: var(--spacing-sm);
  height: 100%; /* 确保布局占满容器高度 */
  overflow: hidden; /* 防止出现双滚动条 */
}

/* 左侧列优化 */
.item-detail-left-column {
  width: 28%;
  height: 100%; /* 确保占满父容器高度 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  overflow: hidden; /* 防止出现双滚动条 */
  margin-left: 20px; /* 添加左边距 */
}

/* 右侧列优化 */
.item-detail-right-column {
  width: 72%;
  height: 100%; /* 确保占满父容器高度 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  overflow: hidden; /* 防止出现双滚动条 */
  border-radius: var(--border-radius-large);
  border: 1px solid var(--item-border-color);
  margin-right: 15px; /* 添加左边距 */
}

/* 添加滚动容器样式 */
.item-detail-scroll-container {
  height: 100%;
  overflow-y: auto;
  padding-right: var(--spacing-sm);
  display: flex; /* 添加flex布局 */
  flex-direction: column; /* 垂直方向排列 */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  scroll-behavior: smooth; /* 平滑滚动 */
}

/* 确保卡片内容可以滚动 */
.item-overview-card,
.price-stats-card,
.physical-attrs-card,
.weapon-stats-card,
.weapon-details-card {
  height: fit-content; /* 根据内容自适应高度 */
  min-height: 0; /* 允许内容收缩 */
  flex-shrink: 0; /* 防止内容被压缩 */
}

/* 滚动条样式 */
.item-detail-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.item-detail-scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

.item-detail-scroll-container::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 3px;
}

/* 增强卡片效果 */
.item-overview-card,
.price-stats-card,
.physical-attrs-card,
.weapon-stats-card,
.weapon-details-card {
  border-radius: var(--border-radius-large);
  box-shadow: var(--item-shadow-medium);
  transition: all 0.3s ease;
  background: var(--item-card-bg);
  padding: 0 !important;
  position: relative;
  z-index: 1;
  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.item-overview-card:hover,
.price-stats-card:hover,
.physical-attrs-card:hover,
.weapon-stats-card:hover,
.weapon-details-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--item-shadow-heavy);
}

/* 增强图片容器效果 */
.item-image-container {
  position: relative;
  display: inline-block;
  width: 200px;
  height: 200px;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--item-shadow-medium);
  transition: all 0.3s ease;
}

.item-image-container:hover {
  transform: scale(1.02);
  box-shadow: var(--item-shadow-heavy);
}

/* 属性列表样式 */
.attr-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  padding: 0 var(--spacing-sm);
}

.attr-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.attr-group-title {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--item-text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.attr-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px dashed var(--border-color);
}

.attr-item:last-child {
  border-bottom: none;
}

.attr-name {
  font-weight: 600;
  color: var(--item-text-primary);
}

.attr-value {
  color: var(--item-text-secondary);
}

/* 平板设备优化 */
@media (max-width: 768px) {
  .attr-list {
    gap: var(--spacing-sm);
  }

  .attr-group-title {
    font-size: var(--font-size-sm);
  }

  .attr-item {
    padding: var(--spacing-xs) 0;
    min-height: 40px;
    align-items: center;
  }

  /* 优化触摸目标 */
  .item-detail-scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 优化卡片间距 */
  .item-overview-card,
  .content-section {
    margin-bottom: 12px;
  }
}

/* 添加加载动画 */
.item-detail-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--item-text-secondary);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 属性列表样式 */
.attr-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-md);
}

.attr-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px dashed var(--border-color);
}

.attr-item:last-child {
  border-bottom: none;
}

.attr-name {
  font-weight: 600;
  color: var(--item-text-primary);
}

.attr-value {
  color: var(--item-text-secondary);
}

/* 属性分组标题 */
.attr-group-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  margin: var(--spacing-md) 0 var(--spacing-sm);
  color: var(--item-text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.attr-group-title:first-child {
  margin-top: 0;
}

/* 图片外边框容器 */
.item-image-border {
  position: relative;
  border-radius: 12px;
  border: 2.5px solid var(--item-border-color);
  background: var(--item-card-bg);
  overflow: hidden;
  width: 100%;
  max-width: 340px;
  margin: 0 auto;
  box-shadow: var(--item-shadow-light);
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 等级角标 */
.grade-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #fff;
  border-radius: 16px;
  padding: 2px 12px;
  font-size: 15px;
  font-weight: 600;
  z-index: 2;
  box-shadow: var(--item-shadow-light);
  pointer-events: none;
}

/* 等级标签色 */
.grade-badge-0 { 
  background: #949595;
  color: #fff;
}
.grade-badge-1 { 
  background: #cdd5d5;
  color: #333;
}
.grade-badge-2 { 
  background: #188D14;
  color: #fff;
}
.grade-badge-3 { 
  background: #658BCE;
  color: #fff;
}
.grade-badge-4 { 
  background: #9b61c8;
  color: #fff;
}
.grade-badge-5 { 
  background: #e8a64e;
  color: #fff;
}
.grade-badge-6 { 
  background: #cb464a;
  color: #fff;
}

/* 等级边框色 */
.grade-border-0 { border-color: #949595; }
.grade-border-1 { border-color: #cdd5d5; }
.grade-border-2 { border-color: #188D14; }
.grade-border-3 { border-color: #658BCE; }
.grade-border-4 { border-color: #9b61c8; }
.grade-border-5 { border-color: #e8a64e; }
.grade-border-6 { border-color: #cb464a; }

/* 等级背景色 */
.grade-bg-0 { background: #949595; }
.grade-bg-1 { background: #cdd5d5; }
.grade-bg-2 { background: #188D14; }
.grade-bg-3 { background: #658BCE; }
.grade-bg-4 { background: #9b61c8; }
.grade-bg-5 { background: #e8a64e; }
.grade-bg-6 { background: #cb464a; }

/* 图片本体 */
.item-image {
  width: 100%;
  height: 160px;
  object-fit: contain;
  background: var(--item-card-bg);
  display: block;
}

/* 卡片整体阴影和圆角 */
.item-overview-card {
  border-radius: 14px;
  box-shadow: var(--item-shadow-medium);
  background: var(--item-card-bg);
  padding: 0 !important;
  position: relative;
  z-index: 1;
}


/* 分组标题样式 */
.attr-table-group {
  margin: 18px 0 0 0;
}

.attr-table-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.attr-table-title-line {
  flex: 1;
  height: 1px;
  background: var(--item-divider-color);
  margin: 0 8px;
}

.attr-table-title-text {
  font-weight: 600;
  font-size: 15px;
  color: var(--item-text-primary);
  white-space: nowrap;
}

/* 属性表格样式 */
.attr-table-list {
  display: flex;
  flex-direction: column;
  background: var(--item-card-bg);
  border-radius: 6px;
  overflow: hidden;
}

.attr-table-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 7px 0;
  border-bottom: 1px dashed var(--item-divider-color);
}

.attr-table-row:last-child {
  border-bottom: none;
}

.attr-table-label {
  font-size: 14px;
  color: var(--item-text-primary);
  font-weight: 600;
  letter-spacing: 0.2px;
  line-height: 1.7;
}

.attr-table-value {
  color: var(--item-text-secondary);
  font-size: 14px;
  font-weight: 400;
  text-align: right;
  word-break: break-all;
}

.effect-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  text-align: left;
}

.effect-item:last-child {
  margin-bottom: 0;
}

.effect-value {
  flex: 1;
}

.effect-battery {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.effect-battery.green {
  background-color: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.effect-battery.red {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.effect-battery.yellow {
  background-color: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

.attr-table-value-bar {
  display: flex;
  align-items: center;
}

.attr-table-bar {
  margin-left: 10px;
  min-width: 120px;
}

.item-info-block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 18px 0 0 0;
  padding: 0 2px;
}

.item-title-row {
  font-size: 20px;
  font-weight: 700;
  color: var(--item-text-primary);
  margin-bottom: 6px;
  line-height: 1.2;
}

.item-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--item-text-primary);
}

.item-price-row {
  font-size: 28px;
  font-weight: 800;
  color: #f44336;
  margin-bottom: 6px;
  line-height: 1.1;
}

.item-price {
  font-size: 28px;
  font-weight: 800;
  color: #f44336;
}

.item-change-row {
  font-size: 15px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.item-change {
  display: flex;
  align-items: center;
  font-weight: 400;
  border-radius: 6px;
}

.item-change-up {
  color: #f44336; /* 上涨用红色 */
  background: var(--price-up-bg);
}

.item-change-down {
  color: #4caf50; /* 下降用绿色 */
  background: var(--price-down-bg);
}

.item-change-percent {
  margin-left: 6px;
  font-size: 14px;
  color: var(--item-text-secondary);
  font-weight: 400;
}

.size-grid {
  display: grid;
  margin-left: 10px;
  vertical-align: middle;
  /* 动态行列由内联样式控制 */
  gap: 2px;
}

.size-grid-cell {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid var(--item-border-color);
  display: inline-block;
}

/* 特殊属性区块美化 */
.attr-section {
  margin-top: 22px;
  margin-bottom: 0;
  padding: 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.attr-group {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-bottom: 18px;
}

.attr-group-title {
  font-size: 19px;
  font-weight: 700;
  color: var(--item-text-primary);
  margin-bottom: 10px;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}

.color-bar {
  display: inline-block;
  width: 4px;
  height: 20px;
  background: #e8a64e;
  border-radius: 2px;
  margin-right: 10px;
  vertical-align: middle;
}

.attr-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.attr-label {
  font-size: 16px;
  color: var(--item-text-primary);
  font-weight: 500;
  letter-spacing: 0.2px;
  line-height: 1.7;
}

.attr-value {
  font-size: 18px;
  color: var(--item-text-primary);
  font-weight: 700;
  text-align: right;
  word-break: break-all;
  letter-spacing: 0.1px;
  line-height: 1.7;
}

.tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  margin-left: 4px;
}

.tag-red {
  background: #f44336;
  color: #fff;
}

.tag-blue {
  background: #3b82f6;
  color: #fff;
}

.tag-gray {
  background: var(--item-border-color);
  color: var(--item-text-primary);
}

@media (max-width: 900px) {
  .content-section {
    padding: 14px 6px 10px 6px !important;
  }
  .attr-label, .attr-value {
    font-size: 15px;
  }
}

/* 响应式布局优化 */
@media (max-width: 1200px) {
  .item-detail-layout {
    gap: 12px;
  }
  
  .item-detail-left-column {
    width: 32%;
  }
  
  .item-detail-right-column {
    width: 68%;
  }
}

@media (max-width: 900px) {
  .item-detail-layout {
    flex-direction: column;
    gap: 16px;
    height: auto;
    padding: 0 8px;
  }

  .item-detail-left-column,
  .item-detail-right-column {
    width: 100%;
    margin: 0;
    border-radius: 12px;
    box-shadow: var(--item-shadow-light);
    border: 1px solid var(--item-border-color);
  }

  .item-detail-left-column {
    margin-bottom: 16px;
  }

  .item-overview-card {
    max-width: 100%;
    min-width: 0;
    padding: 16px !important;
    border-radius: 12px;
  }

  .item-image-border {
    max-width: 100%;
    min-width: 0;
    margin-bottom: 16px;
  }

  .item-image {
    height: 140px;
  }

  .attr-table-title-text {
    font-size: 14px;
    font-weight: 600;
  }

  .attr-table-label,
  .attr-table-value {
    font-size: 13px;
    line-height: 1.5;
  }

  .attr-table-row {
    padding: 8px 0;
    min-height: 44px;
    align-items: center;
  }

  .attr-table-bar {
    min-width: 80px;
  }

  .item-title,
  .item-title-row {
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 8px;
  }

  .item-price,
  .item-price-row {
    font-size: 24px;
    line-height: 1.2;
    margin-bottom: 8px;
  }

  .item-change-row {
    font-size: 14px;
    line-height: 1.4;
  }

  .grade-badge {
    font-size: 13px;
    padding: 4px 10px;
    border-radius: 8px;
  }

  .content-section {
    padding: 16px 12px !important;
    margin: 8px 0;
    border-radius: 12px;
  }

  /* 优化模态框在小屏幕下的显示 */
  .item-detail-modal {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    height: 100vh !important;
    border-radius: 0 !important;
  }

  /* 优化头部布局 */
  .modal-header {
    padding: 12px 16px !important;
    min-height: 60px;
    display: flex;
    align-items: center;
  }

  .header-title {
    font-size: 18px !important;
    font-weight: 600;
  }

  /* 优化操作按钮 - 确保触摸友好 */
  .action-button {
    padding: 8px 12px !important;
    font-size: 14px !important;
    min-height: 44px;
    min-width: 44px;
    border-radius: 8px;
  }

  /* 优化头部按钮区域 */
  .n-space {
    gap: 12px !important;
  }
}

@media (max-width: 600px) {
  .item-detail-layout {
    padding: 0 4px;
    gap: 12px;
  }

  .item-image {
    height: 100px;
  }

  .item-title,
  .item-title-row {
    font-size: 16px;
    line-height: 1.3;
  }

  .item-price,
  .item-price-row {
    font-size: 20px;
    line-height: 1.2;
  }

  .attr-table-label,
  .attr-table-value {
    font-size: 12px;
    line-height: 1.5;
  }

  .attr-table-bar {
    min-width: 60px;
  }

  /* 优化属性表格在小屏幕下的显示 */
  .attr-table-row {
    padding: 6px 0;
    min-height: 40px;
  }

  .attr-table-title {
    margin-bottom: 8px;
  }

  .attr-table-title-text {
    font-size: 13px;
  }

  /* 优化进度条显示 */
  .attr-table-value-bar {
    flex-direction: column;
    align-items: flex-end;
    gap: 6px;
  }

  .attr-table-bar {
    width: 100% !important;
    margin-left: 0 !important;
  }

  /* 优化尺寸网格显示 */
  .size-grid {
    display: none;
  }

  /* 优化价格历史图表 */
  .price-history-chart {
    height: 220px !important;
  }

  /* 优化卡片内边距 */
  .item-overview-card {
    padding: 12px !important;
  }

  .content-section {
    padding: 12px 8px !important;
  }

  /* 优化头部区域 */
  .modal-header {
    padding: 8px 12px !important;
    min-height: 56px;
  }

  .header-title {
    font-size: 16px !important;
  }

  .action-button {
    padding: 6px 10px !important;
    font-size: 13px !important;
    min-height: 40px;
    min-width: 40px;
  }
}

/* 针对超小屏幕的优化 */
@media (max-width: 375px) {
  .item-detail-layout {
    padding: 0 2px;
    gap: 8px;
  }

  .item-detail-modal {
    padding: 0 !important;
  }

  .modal-header {
    padding: 6px 8px !important;
    min-height: 52px;
  }

  .header-title {
    font-size: 15px !important;
    font-weight: 600;
  }

  .action-button {
    padding: 4px 8px !important;
    font-size: 12px !important;
    min-height: 36px;
    min-width: 36px;
    border-radius: 6px;
  }

  .item-image {
    height: 80px;
  }

  .item-title,
  .item-title-row {
    font-size: 14px;
    line-height: 1.3;
  }

  .item-price,
  .item-price-row {
    font-size: 18px;
    line-height: 1.2;
  }

  .attr-table-label,
  .attr-table-value {
    font-size: 11px;
    line-height: 1.4;
  }

  .attr-table-row {
    padding: 4px 0;
    min-height: 36px;
  }

  .attr-table-title-text {
    font-size: 12px;
  }

  .item-overview-card {
    padding: 8px !important;
  }

  .content-section {
    padding: 8px 6px !important;
  }

  .grade-badge {
    font-size: 11px;
    padding: 2px 6px;
  }

  /* 优化按钮间距 */
  .n-space {
    gap: 8px !important;
  }
}



.enlarge-chart-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 900px) {
  .enlarge-chart-modal {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }

  /* 优化图表模态框的内容区域 */
  .enlarge-chart-modal .n-card__content {
    padding: 8px !important;
    height: calc(100vh - 60px);
    overflow-y: auto;
  }

  /* 优化图表模态框的头部 */
  .enlarge-chart-modal .n-card-header {
    padding: 8px 12px !important;
    border-bottom: 1px solid var(--item-border-color);
    background: var(--item-card-bg);
    position: sticky;
    top: 0;
    z-index: 10;
  }
}


</style>